// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';

// ChatViewProvider class that implements WebviewViewProvider
class ChatViewProvider implements vscode.WebviewViewProvider {
	public static readonly viewType = 'ai-chat-view';

	constructor(private readonly _extensionUri: vscode.Uri) {}

	public resolveWebviewView(
		webviewView: vscode.WebviewView,
		context: vscode.WebviewViewResolveContext,
		_token: vscode.CancellationToken,
	) {
		console.log('resolveWebviewView called for:', ChatViewProvider.viewType);

		webviewView.webview.options = {
			// Allow scripts in the webview
			enableScripts: true,
			localResourceRoots: [
				this._extensionUri
			]
		};

		webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
		console.log('WebView HTML set successfully');
	}

	private _getHtmlForWebview(webview: vscode.Webview) {
		return `<!DOCTYPE html>
			<html lang="en">
			<head>
				<meta charset="UTF-8">
				<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src ${webview.cspSource} 'unsafe-inline';">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<title>AI Chat</title>
				<style>
					* {
						margin: 0;
						padding: 0;
						box-sizing: border-box;
					}

					body {
						font-family: var(--vscode-editor-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
						font-size: var(--vscode-editor-font-size, 14px);
						height: 100vh;
						overflow: hidden;
						background-color: var(--vscode-editor-background);
						color: var(--vscode-editor-foreground);
					}

					.chat-container {
						display: flex;
						flex-direction: column;
						height: 100vh;
						background-color: var(--vscode-editor-background);
					}

					.chat-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 12px 16px;
						border-bottom: 1px solid var(--vscode-panel-border);
						background-color: var(--vscode-sideBar-background);
						min-height: 48px;
					}

					.chat-header h3 {
						font-size: 14px;
						font-weight: 600;
						color: var(--vscode-sideBarTitle-foreground);
						margin: 0;
					}

					.settings-btn {
						background: none;
						border: none;
						font-size: 16px;
						cursor: pointer;
						padding: 4px;
						border-radius: 4px;
						transition: background-color 0.2s;
						color: var(--vscode-icon-foreground);
					}

					.settings-btn:hover {
						background-color: var(--vscode-toolbar-hoverBackground);
					}

					.messages-container {
						flex: 1;
						overflow-y: auto;
						padding: 16px;
						background-color: var(--vscode-editor-background);
					}

					.input-container {
						display: flex;
						padding: 12px 16px;
						border-top: 1px solid var(--vscode-panel-border);
						background-color: var(--vscode-sideBar-background);
						gap: 8px;
					}

					#messageInput {
						flex: 1;
						padding: 8px 12px;
						border: 1px solid var(--vscode-input-border);
						border-radius: 6px;
						font-size: var(--vscode-editor-font-size, 14px);
						outline: none;
						background-color: var(--vscode-input-background);
						color: var(--vscode-input-foreground);
					}

					#messageInput:focus {
						border-color: var(--vscode-focusBorder);
						box-shadow: 0 0 0 1px var(--vscode-focusBorder);
					}

					#sendBtn {
						padding: 8px 16px;
						background-color: var(--vscode-button-background);
						color: var(--vscode-button-foreground);
						border: none;
						border-radius: 6px;
						font-size: 14px;
						font-weight: 500;
						cursor: pointer;
						transition: background-color 0.2s;
					}

					#sendBtn:hover {
						background-color: var(--vscode-button-hoverBackground);
					}

					#sendBtn:disabled {
						background-color: var(--vscode-button-secondaryBackground);
						color: var(--vscode-button-secondaryForeground);
						cursor: not-allowed;
					}

					/* Scrollbar styling */
					.messages-container::-webkit-scrollbar {
						width: 6px;
					}

					.messages-container::-webkit-scrollbar-track {
						background: transparent;
					}

					.messages-container::-webkit-scrollbar-thumb {
						background-color: var(--vscode-scrollbarSlider-background);
						border-radius: 3px;
					}

					.messages-container::-webkit-scrollbar-thumb:hover {
						background-color: var(--vscode-scrollbarSlider-hoverBackground);
					}

					/* Message styles */
					.message {
						margin-bottom: 16px;
						max-width: 100%;
						word-wrap: break-word;
					}

					.message-user {
						display: flex;
						flex-direction: column;
						align-items: flex-end;
					}

					.message-ai {
						display: flex;
						flex-direction: column;
						align-items: flex-start;
					}

					.message-content {
						padding: 8px 12px;
						border-radius: 12px;
						max-width: 85%;
						line-height: 1.4;
						font-size: var(--vscode-editor-font-size, 14px);
					}

					.message-user .message-content {
						background-color: var(--vscode-button-background);
						color: var(--vscode-button-foreground);
						border-bottom-right-radius: 4px;
					}

					.message-ai .message-content {
						background-color: var(--vscode-input-background);
						color: var(--vscode-input-foreground);
						border: 1px solid var(--vscode-input-border);
						border-bottom-left-radius: 4px;
					}

					.message-time {
						font-size: 11px;
						color: var(--vscode-descriptionForeground);
						margin-top: 4px;
						opacity: 0.7;
					}

					/* Settings panel styles */
					.settings-panel {
						position: absolute;
						top: 48px;
						right: 16px;
						width: 250px;
						background-color: var(--vscode-dropdown-background);
						border: 1px solid var(--vscode-dropdown-border);
						border-radius: 6px;
						box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
						padding: 16px;
						z-index: 1000;
						display: none;
					}

					.settings-panel h4 {
						margin: 0 0 12px 0;
						font-size: 14px;
						font-weight: 600;
						color: var(--vscode-foreground);
					}

					.setting-item {
						margin-bottom: 12px;
					}

					.setting-item label {
						display: block;
						font-size: 12px;
						color: var(--vscode-foreground);
						margin-bottom: 4px;
						font-weight: 500;
					}

					.setting-item select,
					.setting-item input[type="range"] {
						width: 100%;
						padding: 4px 8px;
						border: 1px solid var(--vscode-input-border);
						border-radius: 4px;
						background-color: var(--vscode-input-background);
						color: var(--vscode-input-foreground);
						font-size: 12px;
					}

					.setting-item select:focus,
					.setting-item input:focus {
						outline: none;
						border-color: var(--vscode-focusBorder);
					}

					.settings-close-btn {
						width: 100%;
						padding: 6px 12px;
						background-color: var(--vscode-button-secondaryBackground);
						color: var(--vscode-button-secondaryForeground);
						border: none;
						border-radius: 4px;
						font-size: 12px;
						cursor: pointer;
						margin-top: 8px;
						transition: background-color 0.2s;
					}

					.settings-close-btn:hover {
						background-color: var(--vscode-button-secondaryHoverBackground);
					}

					/* Typing indicator styles */
					.typing-indicator {
						display: flex;
						align-items: center;
						margin-bottom: 16px;
						padding: 8px 12px;
						background-color: var(--vscode-input-background);
						border: 1px solid var(--vscode-input-border);
						border-radius: 12px;
						border-bottom-left-radius: 4px;
						max-width: 85%;
						font-size: var(--vscode-editor-font-size, 14px);
						color: var(--vscode-descriptionForeground);
						font-style: italic;
					}

					.typing-indicator::after {
						content: '';
						display: inline-block;
						width: 4px;
						height: 4px;
						border-radius: 50%;
						background-color: var(--vscode-descriptionForeground);
						margin-left: 8px;
						animation: typing-pulse 1.5s infinite;
					}

					@keyframes typing-pulse {
						0%, 60%, 100% { opacity: 0.3; }
						30% { opacity: 1; }
					}

					/* Loading state for send button */
					.send-btn-loading {
						opacity: 0.7;
						cursor: not-allowed;
					}

					.send-btn-loading::after {
						content: '';
						display: inline-block;
						width: 12px;
						height: 12px;
						border: 2px solid transparent;
						border-top: 2px solid currentColor;
						border-radius: 50%;
						margin-left: 6px;
						animation: button-spin 1s linear infinite;
					}

					@keyframes button-spin {
						0% { transform: rotate(0deg); }
						100% { transform: rotate(360deg); }
					}
				</style>
			</head>
			<body>
				<div class="chat-container">
					<div class="chat-header">
						<h3>AI Assistant</h3>
						<button class="settings-btn" id="settingsBtn">⚙️</button>
					</div>
					<div class="messages-container" id="messages">
						<!-- Messages will be displayed here -->
					</div>
					<div class="input-container">
						<input type="text" id="messageInput" placeholder="Type your message..." />
						<button id="sendBtn">Send</button>
					</div>

					<!-- Settings Panel -->
					<div id="settingsPanel" class="settings-panel">
						<h4>Settings</h4>
						<div class="setting-item">
							<label for="themeSelect">Theme:</label>
							<select id="themeSelect">
								<option value="auto">Auto (Follow VS Code)</option>
								<option value="light">Light</option>
								<option value="dark">Dark</option>
							</select>
						</div>
						<div class="setting-item">
							<label for="fontSizeRange">Font Size: <span id="fontSizeValue">14px</span></label>
							<input type="range" id="fontSizeRange" min="12" max="18" value="14" step="1">
						</div>
						<div class="setting-item">
							<label for="autoScrollCheck">
								<input type="checkbox" id="autoScrollCheck" checked> Auto-scroll to new messages
							</label>
						</div>
						<button class="settings-close-btn" id="settingsCloseBtn">Close</button>
					</div>
				</div>

				<script>
					// Message display functions
					function addMessage(content, isUser = false) {
						const messagesContainer = document.getElementById('messages');
						const messageDiv = document.createElement('div');
						messageDiv.className = isUser ? 'message message-user' : 'message message-ai';

						const messageContent = document.createElement('div');
						messageContent.className = 'message-content';
						messageContent.textContent = content;

						const messageTime = document.createElement('div');
						messageTime.className = 'message-time';
						messageTime.textContent = new Date().toLocaleTimeString();

						messageDiv.appendChild(messageContent);
						messageDiv.appendChild(messageTime);
						messagesContainer.appendChild(messageDiv);

						scrollToBottom();
					}

					function scrollToBottom() {
						if (autoScroll) {
							const container = document.getElementById('messages');
							container.scrollTo({
								top: container.scrollHeight,
								behavior: 'smooth'
							});
						}
					}

					// Settings panel functions
					function toggleSettings() {
						settingsVisible = !settingsVisible;
						settingsPanel.style.display = settingsVisible ? 'block' : 'none';
					}

					function updateFontSize() {
						const size = fontSizeRange.value;
						fontSizeValue.textContent = size + 'px';
						document.body.style.setProperty('--custom-font-size', size + 'px');

						// Update message font sizes
						const messages = document.querySelectorAll('.message-content');
						messages.forEach(msg => {
							msg.style.fontSize = size + 'px';
						});
					}

					function updateAutoScroll() {
						autoScroll = autoScrollCheck.checked;
					}

					// Typing indicator functions
					function showTypingIndicator() {
						const indicator = document.createElement('div');
						indicator.className = 'typing-indicator';
						indicator.id = 'typingIndicator';
						indicator.textContent = 'AI is typing...';
						document.getElementById('messages').appendChild(indicator);
						scrollToBottom();
						return indicator;
					}

					function removeTypingIndicator() {
						const indicator = document.getElementById('typingIndicator');
						if (indicator) {
							indicator.remove();
						}
					}

					// Input and send functionality
					const messageInput = document.getElementById('messageInput');
					const sendBtn = document.getElementById('sendBtn');

					// Settings functionality
					const settingsBtn = document.getElementById('settingsBtn');
					const settingsPanel = document.getElementById('settingsPanel');
					const settingsCloseBtn = document.getElementById('settingsCloseBtn');
					const fontSizeRange = document.getElementById('fontSizeRange');
					const fontSizeValue = document.getElementById('fontSizeValue');
					const autoScrollCheck = document.getElementById('autoScrollCheck');

					let settingsVisible = false;
					let autoScroll = true;

					function sendMessage() {
						const content = messageInput.value.trim();
						if (!content) return;

						// Add user message
						addMessage(content, true);
						messageInput.value = '';

						// Show loading state
						sendBtn.disabled = true;
						sendBtn.className = 'send-btn-loading';
						sendBtn.textContent = 'Sending';

						// Show typing indicator
						const typingIndicator = showTypingIndicator();

						// Simulate AI reply (delay 1.5-3 seconds for more realistic feel)
						const delay = 1500 + Math.random() * 1500;
						setTimeout(() => {
							// Remove typing indicator
							removeTypingIndicator();

							const aiResponses = [
								'That\\'s an interesting question! Let me think about this...',
								'I\\'d be happy to help you with that. Here\\'s what I suggest:',
								'Great question! Based on what you\\'ve shared, I think...',
								'I understand what you\\'re asking. Let me provide some guidance:',
								'Could you provide more details? That would help me give you a better answer.',
								'That\\'s a great point to consider. Here\\'s my perspective:',
								'Let me break this down for you step by step.',
								'Interesting! I\\'ve seen similar situations before. Here\\'s what usually works:',
								'Good thinking! You might also want to consider...',
								'That makes sense. Have you tried approaching it this way?'
							];
							const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];
							addMessage(randomResponse, false);

							// Reset send button
							sendBtn.disabled = false;
							sendBtn.className = '';
							sendBtn.textContent = 'Send';

							// Focus input for next message
							messageInput.focus();
						}, delay);
					}

					// Event listeners
					sendBtn.addEventListener('click', () => {
						if (canSendMessage()) {
							sendMessage();
						}
					});

					messageInput.addEventListener('keypress', (e) => {
						if (e.key === 'Enter') {
							e.preventDefault();
							if (canSendMessage()) {
								sendMessage();
							}
						}
					});

					// Settings event listeners
					settingsBtn.addEventListener('click', toggleSettings);
					settingsCloseBtn.addEventListener('click', toggleSettings);
					fontSizeRange.addEventListener('input', updateFontSize);
					autoScrollCheck.addEventListener('change', updateAutoScroll);

					// Close settings when clicking outside
					document.addEventListener('click', (e) => {
						if (settingsVisible &&
							!settingsPanel.contains(e.target) &&
							!settingsBtn.contains(e.target)) {
							toggleSettings();
						}
					});

					// Enhanced input management
					messageInput.addEventListener('focus', () => {
						messageInput.style.borderColor = 'var(--vscode-focusBorder)';
					});

					messageInput.addEventListener('blur', () => {
						messageInput.style.borderColor = 'var(--vscode-input-border)';
					});

					// Auto-resize input and enable/disable send button based on content
					messageInput.addEventListener('input', () => {
						const hasContent = messageInput.value.trim().length > 0;
						sendBtn.disabled = !hasContent || sendBtn.classList.contains('send-btn-loading');

						// Update send button appearance based on content
						if (hasContent && !sendBtn.classList.contains('send-btn-loading')) {
							sendBtn.style.opacity = '1';
						} else if (!sendBtn.classList.contains('send-btn-loading')) {
							sendBtn.style.opacity = '0.6';
						}
					});

					// Prevent sending empty messages
					function canSendMessage() {
						return messageInput.value.trim().length > 0 && !sendBtn.disabled;
					}

					// Initialize the interface
					function initializeInterface() {
						// Set initial button state
						sendBtn.disabled = true;
						sendBtn.style.opacity = '0.6';

						// Add welcome message
						setTimeout(() => {
							addMessage('👋 Hello! I\\'m your AI assistant. How can I help you today?', false);
						}, 500);

						// Add example user message
						setTimeout(() => {
							addMessage('I need help with my code', true);
						}, 1500);

						// Add example AI response
						setTimeout(() => {
							addMessage('I\\'d be happy to help! Please share your code and describe the issue you\\'re facing. You can also ask me about programming concepts, debugging tips, or best practices.', false);
						}, 2500);

						// Focus the input after examples are loaded and show a helpful tip
						setTimeout(() => {
							messageInput.focus();
							messageInput.placeholder = 'Ask me anything about coding...';
						}, 3500);
					}

					// Add some example messages when the page loads
					window.addEventListener('load', initializeInterface);
				</script>
			</body>
			</html>`;
	}
}

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

	// Use the console to output diagnostic information (console.log) and errors (console.error)
	// This line of code will only be executed once when your extension is activated
	console.log('Congratulations, your extension "ai-extension-demo" is now active!');

	// Register the webview view provider
	const chatViewProvider = new ChatViewProvider(context.extensionUri);
	const disposableViewProvider = vscode.window.registerWebviewViewProvider(
		ChatViewProvider.viewType,
		chatViewProvider,
		{
			webviewOptions: {
				retainContextWhenHidden: true
			}
		}
	);
	context.subscriptions.push(disposableViewProvider);

	console.log('WebviewViewProvider registered for:', ChatViewProvider.viewType);

	// The command has been defined in the package.json file
	// Now provide the implementation of the command with registerCommand
	// The commandId parameter must match the command field in package.json
	const disposable = vscode.commands.registerCommand('ai-extension-demo.helloWorld', () => {
		// The code you place here will be executed every time your command is executed
		// Display a message box to the user
		vscode.window.showInformationMessage('Hello World from ai_extension_demo!');
	});

	context.subscriptions.push(disposable);
}

// This method is called when your extension is deactivated
export function deactivate() {}
